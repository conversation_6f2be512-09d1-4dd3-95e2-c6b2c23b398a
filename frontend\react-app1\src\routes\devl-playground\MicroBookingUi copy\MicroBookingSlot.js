import React, { useState, useEffect } from 'react';
import {
    Card,
    Checkbox,
    Select,
    Row,
    Col,
    Button,
    message,
    Spin,
    Alert,
    Tag,
    Radio,
} from 'antd';
import moment from 'moment';
import http_utils from '../../../util/http_utils';
import './booking.css';
import FormBuilder from 'antd-form-builder';
import { render } from '@testing-library/react';
import TimePickerWidget from '../../../components/wify-utils/TimePickerWidget';

const { Option } = Select;

const MicroBookingSlotUp = ({
    savedSlots,
    disableSaveButton,
    isBookingAreadydone,
    allSrvcPrvdrs,
    allowedSrvcPrvdrs,
    formRef,
    initialValues,
    current_prvdr,
    editMode,
    default_srvc_prvdr,
}) => {
    const forceUpdate = FormBuilder.useForceUpdate();
    const [selectedWeek, setSelectedWeek] = useState(null);
    const [weekOptions, setWeekOptions] = useState([]);
    const [weekData, setWeekData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [viewData, setViewData] = useState(undefined);
    const [selectedSlots, setSelectedSlots] = useState({});
    const [error, setError] = useState(null);
    const [capacityData, setCapacityData] = useState(null);
    const [selectedPrvdr, setSelectedPrvdr] = useState(null);

    // Generate week options
    useEffect(() => {
        generateWeekOptions();
    }, [selectedPrvdr]);

    useEffect(() => {
        if (savedSlots) {
            setSelectedSlots(savedSlots);

            fetchCapacityData(current_prvdr);
        }
    }, [savedSlots, selectedPrvdr]);

    const generateWeekOptions = () => {
        const weeks = [];
        const today = moment();

        // First week: today to upcoming Saturday
        const firstWeekEnd = today.clone().day(6); // 6 = Saturday
        if (firstWeekEnd.isBefore(today)) {
            firstWeekEnd.add(1, 'weeks'); // if today is Sunday, get next Saturday
        }

        weeks.push({
            value: `${today.format('YYYY-MM-DD')}_${firstWeekEnd.format('YYYY-MM-DD')}`,
            label: `${today.format('Do MMM')} - ${firstWeekEnd.format('Do MMM ')}`,
            startDate: today.format('YYYY-MM-DD'),
            endDate: firstWeekEnd.format('YYYY-MM-DD'),
        });

        // Generate next 4 weeks (Sun–Sat)
        const nextWeekStart = firstWeekEnd.clone().add(1, 'days'); // Sunday after first Saturday

        for (let i = 0; i < 4; i++) {
            const weekStart = nextWeekStart.clone().add(i * 7, 'days');
            const weekEnd = weekStart.clone().add(6, 'days');

            weeks.push({
                value: `${weekStart.format('YYYY-MM-DD')}_${weekEnd.format('YYYY-MM-DD')}`,
                label: `${weekStart.format('Do MMM')} - ${weekEnd.format('Do MMM ')}`,
                startDate: weekStart.format('YYYY-MM-DD'),
                endDate: weekEnd.format('YYYY-MM-DD'),
            });
        }

        setWeekOptions(weeks);
    };

    const refreshPage = () => {
        forceUpdate();
    };

    // Fetch org settings to get generated slots
    const fetchOrgSettings = () => {
        if (loading) return;

        setLoading(true);
        setViewData(undefined);
        setError(undefined);
        let params = {
            org_id: selectedPrvdr,
        };
        const onComplete = (resp) => {
            console.log('Availability slots response:', resp);
            setViewData(resp.data);
            setLoading(false);
        };

        const onError = (error) => {
            console.error('Error fetching availability slots:', error);
            setViewData(undefined);
            setError(http_utils.decodeErrorToMessage(error));
            setLoading(false);
        };

        http_utils.performGetCall(
            '/services/availability-slots',
            params,
            onComplete,
            onError
        );
    };

    const fetchCapacityData = (prvdrId) => {
        if (loading) return;

        setLoading(true);
        setCapacityData(undefined);
        setError(undefined);
        let params = {
            srvc_prvdr_id: prvdrId,
        };
        console.log('params', params);
        const onComplete = (resp) => {
            console.log('Availability slots response:', resp);
            setCapacityData(resp.data);
            setLoading(false);
        };

        const onError = (error) => {
            console.error('Error fetching availability slots:', error);
            setCapacityData(undefined);
            setError(http_utils.decodeErrorToMessage(error));
            setLoading(false);
        };

        http_utils.performGetCall(
            '/services/capacity',
            params,
            onComplete,
            onError
        );
    };

    // Fetch week data when week is selected
    const fetchWeekData = async (weekValue) => {
        const selectedWeekOption = weekOptions.find(
            (w) => w.value === weekValue
        );
        if (!selectedWeekOption) return;

        try {
            setLoading(true);

            // Generate days for the selected week
            const startDate = moment(selectedWeekOption.startDate);
            const endDate = moment(selectedWeekOption.endDate);
            const days = [];

            let currentDate = startDate.clone();
            while (currentDate.isSameOrBefore(endDate)) {
                days.push({
                    date: currentDate.format('YYYY-MM-DD'),
                    dayName: currentDate.format('dddd'),
                    displayDate: currentDate.format('Do MMM'),
                });
                currentDate.add(1, 'day');
            }

            // Call API to get week-specific booking data (this would be a real API call)
            // For now, we'll just simulate it
            console.log(
                'Fetching booking data for week:',
                selectedWeekOption.startDate,
                'to',
                selectedWeekOption.endDate
            );

            setWeekData({
                ...selectedWeekOption,
                days: days,
            });

            // Initialize selected slots for each day
            const initialSlots = {};
            days.forEach((day) => {
                initialSlots[day.date] = [];
            });
            setSelectedSlots(initialSlots);
        } catch (error) {
            console.error('Error fetching week data:', error);
            message.error('Failed to fetch week data');
        } finally {
            setLoading(false);
        }
    };

    // Handle week selection
    const handleWeekSelect = (value) => {
        fetchOrgSettings();
        setSelectedWeek(value);
        fetchWeekData(value);
    };

    // Handle slot selection for a specific day
    const handleSlotSelection = (date, slotValue) => {
        setSelectedSlots((prev) => {
            const daySlots = prev[date] || [];
            const isSelected = daySlots.includes(slotValue);

            const updatedSlots = {
                ...prev,
                [date]: isSelected
                    ? daySlots.filter((slot) => slot !== slotValue)
                    : [...daySlots, slotValue],
            };

            // Save logic immediately after update
            const totalSelectedSlots = Object.values(updatedSlots).reduce(
                (total, slots) => total + slots.length,
                0
            );

            if (totalSelectedSlots === 0) {
                message.warning('Please select at least one slot');
            } else {
                message.success(
                    `Successfully saved ${totalSelectedSlots} slots across ${
                        Object.keys(updatedSlots).filter(
                            (date) => updatedSlots[date].length > 0
                        ).length
                    } days`
                );

                console.log('Selected slots:', updatedSlots);
            }
            formRef.setFieldsValue({ booked_slots: updatedSlots });

            refreshPage();

            return updatedSlots;
        });
    };

    // Get generated slots from org settings
    const getGeneratedSlots = () => {
        if (!viewData || !viewData.form_data.generated_slots) {
            // Return demo slots if org settings not available
            return [
                { value: '09:00AM - 10:00AM', label: '09:00AM - 10:00AM' },
                { value: '10:00AM - 11:00AM', label: '10:00AM - 11:00AM' },
                { value: '11:00AM - 12:00PM', label: '11:00AM - 12:00PM' },
                { value: '12:00PM - 01:00PM', label: '12:00PM - 01:00PM' },
                { value: '01:00PM - 02:00PM', label: '01:00PM - 02:00PM' },
                { value: '02:00PM - 03:00PM', label: '02:00PM - 03:00PM' },
                { value: '03:00PM - 04:00PM', label: '03:00PM - 04:00PM' },
                { value: '04:00PM - 05:00PM', label: '04:00PM - 05:00PM' },
            ];
        }

        return viewData.form_data.generated_slots;
    };

    const handleBookingRequiredChange = (value) => {
        //setIsBookingRequired(value);
        console.log('value', value);
    };

    const handlePrvdrSelector = (value) => {
        setSelectedPrvdr(value);
        fetchCapacityData(value);
        //if user changes prvdr, reset selected slots in creation mode
        setSelectedSlots({});
    };

    const getMeta = () => {
        const isBookingRequired = formRef?.getFieldValue('booking_required');
        console.log('isBookingRequired', isBookingRequired);
        const isSrvcPrvdrSelected = formRef?.getFieldValue('new_prvdr');
        const isCapcityEnabledFrSelectedPrvdr =
            capacityData?.enable_capacity_module;
        const prvdrNotEnabledCapacity =
            isSrvcPrvdrSelected && !isCapcityEnabledFrSelectedPrvdr;
        const isSlotsBooked = Object.values(selectedSlots).reduce(
            (total, daySlots) => total + daySlots.length,
            0
        );

        return {
            columns: 4,
            formItemLayout: null,
            initialValues: initialValues,

            fields: [
                {
                    key: 'booking_required',
                    label: <b>Booking Required</b>,
                    colSpan: 4,
                    widget: 'radio-group',
                    options: [
                        { label: 'Yes', value: true },
                        { label: 'No', value: false },
                    ],
                    onChange: (value) => {
                        // handleBookingRequiredChange(value);
                        // If booking is required and default provider exists
                       // formRef.setFieldsValue({ booking_required: value });
                        if (value === true && default_srvc_prvdr) {
                            formRef.setFieldsValue({
                                new_prvdr: default_srvc_prvdr,
                            });
                            handlePrvdrSelector(default_srvc_prvdr); // call your change handler
                        }
                        refreshPage();
                    },
                },
                ...(isBookingRequired
                    ? [
                          {
                              key: 'new_prvdr', //need to send this service_provider_id key if req is in creation mode + default prvdr present
                              label: 'Select Service Provider',
                              widget: 'select',
                              colSpan: 2,
                              options: allSrvcPrvdrs,
                              onChange: (value) => {
                                  handlePrvdrSelector(value);
                                  refreshPage();
                              },
                          },
                      ]
                    : []),
                ...(isBookingRequired &&
                isSrvcPrvdrSelected &&
                isCapcityEnabledFrSelectedPrvdr &&
                !isSlotsBooked
                    ? [
                          {
                              key: 'select_slot',
                              label: 'Select Slot',
                              widget: 'select',
                              colSpan: 2,
                              options: weekOptions,
                              onChange: (value) => {
                                  handleWeekSelect(value);
                                  refreshPage();
                              },
                          },
                      ]
                    : []),
                ...(prvdrNotEnabledCapacity && isBookingRequired
                    ? [
                          {
                              key: 'request_req_date',
                              label: 'Req. Service Date',
                              colSpan: 2,
                              widgetProps: {
                                  disabledDate: (current) =>
                                      current &&
                                      current > moment().add(2, 'month'),
                                  style: { width: '100%' },
                                  onChange: (value, dateString) => {
                                      formRef.setFieldsValue({
                                          request_req_date:
                                              moment.utc(dateString),
                                      });
                                  },
                              },
                              widget: 'date-picker',
                              //   required:
                              //       this.isSrvcReqDateMandatoryWhileCreation(),
                          },
                          {
                              key: 'start_time',
                              label: 'Start Time',
                              widget: TimePickerWidget,
                              //   widgetProps: {
                              //       beginLimit: startOfDay,
                              //       endLimit: endOfDay,
                              //       step: 15,
                              //       onChange: (value) => {
                              //           refreshForm();
                              //       },
                              //   },
                              colSpan: 2,
                          },
                          {
                              key: 'end_time',
                              label: 'End Time',
                              widget: TimePickerWidget,
                              //   widgetProps: {
                              //       beginLimit: startTimeFrEndTime
                              //           ? startTimeFrEndTime
                              //           : startOfDay,
                              //       endLimit: endOfDay,
                              //       step: 15,
                              //   },
                              colSpan: 2,
                          },
                      ]
                    : []),
                {
                    key: 'booked_slots',
                    className: 'gx-d-none',
                    widgetProps: {
                        hidden: true,
                    },
                },
                ...(isBookingRequired &&
                isSrvcPrvdrSelected &&
                isCapcityEnabledFrSelectedPrvdr
                    ? [
                          {
                              key: 'booking_slots',
                              colSpan: 4,
                              render: () => {
                                  const hasSelectedSlots = Object.values(
                                      selectedSlots
                                  ).some((slots) => slots.length > 0);

                                  // ✅ Show only selected slot summary if slots are selected
                                  if (hasSelectedSlots) {
                                      return (
                                          <div className="selected-slot-summary">
                                              <div
                                                  style={{
                                                      fontSize: '14px',
                                                      fontWeight: 'bold',
                                                      marginBottom: '12px',
                                                      color: '#333',
                                                  }}
                                              >
                                                  SELECTED SLOT
                                              </div>

                                              {Object.entries(
                                                  selectedSlots
                                              ).map(([date, slots]) => {
                                                  if (slots.length === 0)
                                                      return null;

                                                  return (
                                                      <div
                                                          className="selected-slot-card gx-border-2 gx-border-green"
                                                          key={date}
                                                      >
                                                          <span className="selected-slot-day gx-d-flex gx-justify-content-between">
                                                              <div>
                                                                  {moment(
                                                                      date
                                                                  ).format(
                                                                      'dddd Do MMM'
                                                                  )}
                                                              </div>
                                                              {!editMode && (
                                                                  <div>
                                                                      <Button
                                                                          type="primary"
                                                                          size="small"
                                                                          onClick={() => {
                                                                              // Clear selected slots or toggle to full booking card
                                                                              setSelectedSlots(
                                                                                  {}
                                                                              );
                                                                              formRef.setFieldsValue(
                                                                                  {
                                                                                      booked_slots:
                                                                                          undefined,
                                                                                  }
                                                                              );
                                                                          }}
                                                                          style={{
                                                                              padding: 0,
                                                                          }}
                                                                      >
                                                                          Clear
                                                                      </Button>
                                                                  </div>
                                                              )}
                                                          </span>
                                                          <hr className="selected-slot-divider" />
                                                          <div className="selected-slot-tags">
                                                              {slots.map(
                                                                  (slot) => (
                                                                      <Tag
                                                                          key={`${date}-${slot}`}
                                                                          color="success"
                                                                          className="selected-slot-tag"
                                                                      >
                                                                          {slot}
                                                                      </Tag>
                                                                  )
                                                              )}
                                                          </div>
                                                      </div>
                                                  );
                                              })}
                                          </div>
                                      );
                                  }

                                  // 🟩 Else show the full slot booking card
                                  return (
                                      <>
                                          {weekData && (
                                              <Card className="booking-scroll-card">
                                                  <div className="booking-scroll-container">
                                                      {weekData?.days.map(
                                                          (day) => (
                                                              <div
                                                                  key={day.date}
                                                                  className="booking-day-card"
                                                              >
                                                                  <div className="booking-day-header">
                                                                      {
                                                                          day.dayName
                                                                      }{' '}
                                                                      <br />
                                                                      {
                                                                          day.displayDate
                                                                      }
                                                                  </div>

                                                                  <div className="slot-grid">
                                                                      {getGeneratedSlots()?.map(
                                                                          (
                                                                              slot
                                                                          ) => {
                                                                              const isSelected =
                                                                                  selectedSlots[
                                                                                      day
                                                                                          .date
                                                                                  ]?.includes(
                                                                                      slot.value
                                                                                  );

                                                                              return (
                                                                                  <div
                                                                                      key={`${day.date}-${slot.value}`}
                                                                                      onClick={() =>
                                                                                          handleSlotSelection(
                                                                                              day.date,
                                                                                              slot.value
                                                                                          )
                                                                                      }
                                                                                      className={`slot-box ${isSelected ? 'slot-box-selected' : ''}`}
                                                                                  >
                                                                                      <div>
                                                                                          {slot.label ||
                                                                                              slot.value}
                                                                                      </div>
                                                                                  </div>
                                                                              );
                                                                          }
                                                                      )}
                                                                  </div>
                                                              </div>
                                                          )
                                                      )}
                                                  </div>
                                              </Card>
                                          )}
                                      </>
                                  );
                              },
                          },
                      ]
                    : []),
            ],
        };
    };
    return (
        <div className="booking-wrapper">
            <FormBuilder
                meta={getMeta()}
                form={formRef}
                initialValues={initialValues}
            ></FormBuilder>
        </div>
    );
};

export default MicroBookingSlotUp;
